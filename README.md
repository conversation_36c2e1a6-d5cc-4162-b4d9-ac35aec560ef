python msgpack2parquet.py \
  --src /Users/<USER>/Projects/scalpel_new/daily_combined_data_coinapi \
  --dst /Users/<USER>/Projects/scalpel_new/parquet_raw \
  --workers 16 \
  --dates 2025-07-02

# PRECOMPUTE INDICATORS
python precompute_indicators.py \
  --root-dir   parquet_copy \
  --dst-dir    parquet_processed \
  --symbol     XRPUSDC \
  --cfg        ./strategyConfig_scalp.json \
  --start      2025-01-01 \
  --end        2025-02-01 \
  --n-workers  16 \
  --overwrite

# PRECOMPUTE INDICATORS
python precompute_features.py --config strategyConfig_scalp_1s.json --raw-data-dir parquet_raw --output-dir parquet_processed --start 2025-06-25 --end 2025-06-26

python precompute_features.py --config temp_config_1s.json --raw-data-dir parquet_raw --output-dir parquet_processed --start 2025-06-25 --end 2025-06-26


python precompute_features.py --config strategyConfig_scalp_1s_fixed.json --raw-data-dir parquet_raw --output-dir parquet_processed --start 2025-07-05 --end 2025-07-06


python precompute_features.py --config strategyConfig_scalp_5m_fixed.json --raw-data-dir parquet_raw --output-dir parquet_processed --start 2025-07-04 --end 2025-07-04

python create_continuous_ohlcv.py


python live_trading.py --cfg strategyConfig_scalp_1s.json --use-1s-decisions --log-level INFO

# TAKE PROFIT MODES

This trading system supports two Take Profit (TP) modes:

## 1. Risk-Reward Target Mode (Default)
- **Configuration**: `"tpMode": "rrTarget"`
- **Description**: TP is calculated as a multiple of the Stop Loss distance
- **Formula**: `TP Distance = SL Distance × rrTarget`
- **Example**: If SL is 1% away and rrTarget is 2.0, TP will be 2% away

## 2. Capital Percentage Mode
- **Configuration**: `"tpMode": "capitalPercentage"`
- **Description**: TP is set to achieve a specific percentage profit of initial capital
- **Formula**: `TP Distance = (Initial Capital × tpCapitalPercentage) / Position Size`
- **Example**: With 100 EUR initial capital and 1% target, TP closes when trade profits 1 EUR

### Configuration Parameters:
```json
{
  "tradeParams": {
    "tpMode": "capitalPercentage",           // "rrTarget" or "capitalPercentage"
    "tpCapitalPercentage": 1.0,             // Percentage of initial capital (1.0 = 1%)
    "rrTarget": 2.0                         // Used when tpMode is "rrTarget"
  }
}
```

### Usage Examples:

**Conservative Capital-Based TP (0.5% of capital):**
```json
"tpMode": "capitalPercentage",
"tpCapitalPercentage": 0.5
```

**Aggressive Capital-Based TP (2% of capital):**
```json
"tpMode": "capitalPercentage",
"tpCapitalPercentage": 2.0
```

**Traditional Risk-Reward TP:**
```json
"tpMode": "rrTarget",
"rrTarget": 2.0
```

Both simulation (`simulate_trading_new.py`) and live trading (`live_trading.py`) support both TP modes.